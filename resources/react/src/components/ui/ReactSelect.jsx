import React, { forwardRef, useImperativeHandle, useRef, useState } from "react";
import Select, { components } from "react-select";
import CreatableSelect from "react-select/creatable";

const floatingLabelStyles = {
    container: provided => ({
        ...provided,
        position: "relative",
        cursor: "pointer",
    }),
    control: (provided, state) => ({
        ...provided,
        minHeight: state.selectProps?.height,
        maxHeight: state.selectProps?.height,
        borderTopRightRadius: state.selectProps.radius ? 0 : "",
        borderBottomRightRadius: state.selectProps.radius ? 0 : "",
        backgroundColor: state?.selectProps?.showbg ? "#f5f8fa" : "",
        border:
            state.isFocused && state?.selectProps?.showborder
                ? "1px solid #4f158c !important"
                : state.selectProps.showborder
                  ? "1px solid #6f6f6f"
                  : "none",
        borderRadius: state.selectProps.radius ? "8px 0 0 8px" : "8px",
        transition: "border-color 0.2s, box-shadow 0.2s",
        alignItems: "center",
        outline: "none",
    }),
    valueContainer: provided => ({
        ...provided,
        overflow: "visible",
    }),
    placeholder: (provided, state) => ({
        ...provided,
        position: "absolute",
        top: state.hasValue || state.selectProps.inputValue ? -14 : "3px",
        fontWeight: state.hasValue || state.selectProps.inputValue ? "500 !important" : "normal",
        padding: state.hasValue || state.selectProps.inputValue ? "0px 6px" : "",
        left: 0,
        color: state.hasValue || state.selectProps.inputValue ? "#4F158C" : "#5E6278",
        background:
            state.hasValue || state.selectProps.inputValue
                ? state.selectProps.bgColor
                : "transparent",
        transition: "top 0.1s, font-size 0.1s",
        fontSize: state.hasValue || state.selectProps.inputValue ? 14 : 14,
        zIndex: 1,
        maxWidth: "100%",
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
        "::after": state.selectProps.required && {
            content: '" *"',
            color: "red",
            fontSize: "inherit",
        },
    }),
    indicatorsContainer: provided => ({
        ...provided,
        maxHeight: "34px",
    }),
};

const CustomValueContainer = ({ children, ...props }) => {
    return (
        <components.ValueContainer {...props}>
            <components.Placeholder {...props} isFocused={props.isFocused}>
                {props.selectProps.placeholder + ""}
            </components.Placeholder>
            {React.Children.map(children, child =>
                child && child.type !== components.Placeholder ? child : null,
            )}
        </components.ValueContainer>
    );
};

const DropdownIndicator = props => {
    return (
        components.DropdownIndicator && (
            <components.DropdownIndicator {...props}>
                <i
                    className="fa-solid fa-chevron-down
                        text-black"
                ></i>
            </components.DropdownIndicator>
        )
    );
};

const customStyles = {
    ...floatingLabelStyles,
    option: (provided, state) => ({
        ...provided,
        backgroundColor: state.isDisabled ? "transparent"  : state.isSelected ? "#4F158C" : state.isFocused ? "#f7efff" : "white",
        color: state.isDisabled ? "#b5b5c3"  : state.isSelected ? "white" : state.isFocused ? "#4F158C" : "#181C32",
        cursor: state.isDisabled ? "not-allowed" : "pointer",
    }),
    menu: (provided, state) => ({
        ...provided,
        zIndex: "9999999 !important",
        width: state?.selectProps?.width || "100%",
        boxShadow: "0px 10px 15px rgba(0, 0, 0, 0.1)",
    }),
    menuPortal: provided => ({
        ...provided,
        zIndex: "9999999 !important",
    }),
    menuList: provided => ({
        ...provided,
        zIndex: "9999999 !important",
        boxShadow: "0px 10px 15px rgba(0, 0, 0, 0.1)",
    }),
    singleValue: (provided, state) => ({
        ...provided,
        position: "absolute",
        ...(state.selectProps.isEdit
            ? {
                  maxWidth: "calc(100% - 15px)",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
              }
            : {}),
        top: 3,
        color: "#181C32",
        fontSize: "1.1rem",
        fontWeight: "500",
        whiteSpace: "nowrap",
    }),
};

const CustomOption = ({
    innerRef,
    innerProps,
    data,
    isSelected,
    isFocused,
    currentCurrencySymbol,
}) => (
    <>
        <div
            ref={innerRef}
            {...innerProps}
            className={`custom-option d-flex align-items-center justify-content-between p-2 pe-auto cursor-pointer ${
                isSelected ? "bg-selected text-black" : isFocused ? "bg-focused text-primary" : ""
            }`}
            style={{
                backgroundColor: isSelected ? "#4F158C" : isFocused ? "#f7efff" : "white",
            }}
        >
            <div style={{ maxWidth: "250px", overflowWrap: 'break-word' }}>
                <div>
                    <strong style={{ color: isSelected ? "#fff" : "" }}>{data?.label}</strong>
                </div>
                <div className={`${isSelected ? "dropdown-menu-cost" : "text-muted"}`}>
                    {data?.sku}
                </div>
            </div>
            <div className="text-end">
                {data.item_type == 1 ? (
                    <div className={`${isSelected ? "dropdown-menu-cost" : ""}`}>
                        Stock:
                        <span className={`ps-1 ${data?.stock > -1 ? 'item-ledger-stock' : 'text-danger'}`}>
                            {data?.stock} {data?.unit}
                        </span>
                    </div>
                ) : (
                    ""
                )}
                <div className={`${isSelected ? "dropdown-menu-cost" : ""}`}>
                    Last Sales Price:
                    <span className="ps-1" style={{ color: isSelected ? "#fff" : "" }}>
                        {currentCurrencySymbol ?? "₹"}
                        {data?.selling_price}
                    </span>
                </div>
                <div className={`${isSelected ? "dropdown-menu-cost" : ""}`}>
                    Last Purchase Price:
                    <span className="ps-1" style={{ color: isSelected ? "#fff" : "" }}>
                        {currentCurrencySymbol ?? "₹"}
                        {data?.purchase_cost}
                    </span>
                </div>
            </div>
        </div>
        <hr />
    </>
);

const CustomPartyOption = ({ innerRef, innerProps, data, isSelected, isFocused }) => {
    return (
        <>
            <div
                ref={innerRef}
                {...innerProps}
                className={`custom-option border-0 justify-content-between p-2 pe-auto cursor-pointer details ${
                    isSelected
                        ? "bg-selected text-white"
                        : isFocused
                          ? "bg-focused text-primary"
                          : ""
                }`}
                style={{
                    backgroundColor: isSelected ? "#4F158C" : isFocused ? "#f7efff" : "white",
                }}
            >
                <div>
                    <strong className={`${isSelected ? "drop-shadow-sm" : "label"}`}>
                        {data?.label}
                    </strong>
                </div>
                <div className={`${isSelected ? "drop-shadow-sm" : "fs-6 detail"}`}>
                    {data?.details}
                </div>
                <div>
                    <p className={`${isSelected ? "drop-shadow-sm" : "address"}`}>
                        {data?.address}
                    </p>
                </div>
            </div>
            <hr />
        </>
    );
};

const ReactSelect = forwardRef(
    (
        {
            defaultValue,
            options,
            value,
            onChange,
            placeholder,
            className,
            customLabel = "",
            onKeyDown,
            onKeyUp,
            styles,
            islabel,
            isCreatable = false,
            name,
            portal = false,
            required = false,
            radius,
            showborder = true,
            showbg = false,
            width,
            bgColor = "#FFFFFF",
            height = "34px",
            isEdit,
            handleOpen,
            position = "bottom",
            defaultLabel = "",
            index,
            onMenuScrollToBottom,
            customFilter,
            isMulti = false,
            defaultMenuIsOpen = false,
            isDisabled= false
        },
        ref,
    ) => {
        const [isModalOpen, setIsModalOpen] = useState(defaultMenuIsOpen);
        const onSelectChange = value => {
            setIsModalOpen(false);
            onChange(value);
        };
        const selectRef = useRef(null);
        useImperativeHandle(ref, () => ({
            focus: () => {
                selectRef.current?.focus();
            },
            getSelectedValue: () => {
                return selectRef.current?.props.value || null;
            },
            getFocusedOption: () => {
                return selectRef.current?.state?.focusedOption || null;
            },
        }));
        const CustomSelect = isCreatable ? CreatableSelect : Select;
        const components = {
            ...(islabel !== "no" && { ValueContainer: CustomValueContainer }),
            IndicatorSeparator: () => null,
            DropdownIndicator: isEdit ? () => null : DropdownIndicator,
        };

        const filterOption = (candidate, input) => {
            return (
                candidate.data.__isNew__ ||
                candidate.label?.toLowerCase().includes(input?.toLowerCase()) ||
                (customLabel === "party" &&
                    (candidate.data.phone_1?.toLowerCase().includes(input?.toLowerCase()) ||
                     candidate.data.phone_2?.toLowerCase().includes(input?.toLowerCase())))
            );
        };

        const filterItemOption = (candidate, input) => {
            const inputWords = input?.toLowerCase().split(/\s+/).filter(Boolean); // split by spaces

            const label = candidate.label?.toLowerCase();
            const sku = candidate.data.sku?.toLowerCase() || "";

            const matchesAllWords = inputWords.every(word =>
                label.includes(word) || (customLabel === "item" && sku.includes(word))
            );

            return candidate.data.__isNew__ || matchesAllWords;
        };

        return (
            <>
                {customLabel === "item" || customLabel === "party" ? (
                    <>
                        <CustomSelect
                            defaultValue={options?.find(option => option.value == defaultValue)}
                            value={
                                value === null
                                    ? null
                                    : options?.find(option => option.value == value)
                            }
                            components={{
                                Option: customLabel === "item" ? CustomOption : CustomPartyOption,
                                ...(customLabel === "party" && {
                                    ValueContainer: CustomValueContainer,
                                }),
                                IndicatorSeparator: () => null,
                                DropdownIndicator: isEdit ? () => null : DropdownIndicator,
                            }}
                            styles={styles ? { ...customStyles, ...styles } : customStyles}
                            required={required}
                            placeholder={placeholder}
                            options={
                                defaultLabel !== ""
                                    ? [{ value: null, label: defaultLabel }, ...options]
                                    : options
                            }
                            name={name}
                            onChange={onChange}
                            className={className}
                            onKeyDown={onKeyDown}
                            onKeyUp={onKeyUp}
                            ref={selectRef}
                            menuPortalTarget={portal && document.body} //customLabel === "item"
                            radius={radius}
                            showborder={showborder}
                            showbg={showbg}
                            width={width && width}
                            bgColor={bgColor}
                            height={height}
                            isEdit={isEdit}
                            onInputChange={customFilter}
                            filterOption={customLabel === "item" ? filterItemOption : filterOption}
                            menuPlacement={position}
                            inputProps={{
                                autoComplete: "off",
                                autoCorrect: "off",
                                spellCheck: "off",
                            }}
                            onMenuScrollToBottom={onMenuScrollToBottom}
                        />
                        {isEdit && value && (
                            <button
                                onClick={() =>
                                    handleOpen(selectRef.current?.props?.value?.value, index)
                                }
                                type="button"
                                className="edit-icon focus-icon-btn position-absolute bg-transparent btn-transparent ml-2 p-0"
                            >
                                <i className="fas fa-pencil text-primary"></i>
                            </button>
                        )}
                    </>
                ) : (
                    <>
                        <CustomSelect
                            isMulti={isMulti}
                            defaultMenuIsOpen={isModalOpen}
                            onInputChange={customFilter}
                            defaultValue={options?.find(option => option.value == defaultValue)}
                            key={value ? value : "default"}
                            options={
                                defaultLabel !== "" && options
                                    ? [{ value: null, label: defaultLabel }, ...options]
                                    : options
                            }
                            isOptionDisabled={(option) => option.isDisabled}
                            components={components}
                            value={
                                value === null
                                    ? null
                                    : isMulti
                                      ? value
                                      : options?.find(option => option.value == value)
                            }
                            onChange={onSelectChange}
                            name={name}
                            placeholder={placeholder}
                            styles={styles ? { ...customStyles, ...styles } : customStyles}
                            onKeyDown={onKeyDown}
                            filterOption={filterOption}
                            onKeyUp={onKeyUp}
                            ref={selectRef}
                            required={required}
                            menuPortalTarget={customLabel !== "modal" && document.body}
                            radius={radius}
                            showborder={showborder}
                            showbg={showbg}
                            width={width && width}
                            bgColor={bgColor}
                            isEdit={isEdit}
                            height={height}
                            menuPlacement={position}
                            inputId={className}
                            inputProps={{
                                autoComplete: "off",
                                autoCorrect: "off",
                                spellCheck: "off",
                            }}
                            isDisabled={isDisabled}
                        />
                        {isEdit && value && (
                            <button
                                onClick={() =>
                                    handleOpen(
                                        selectRef.current?.props?.value?.value,
                                        index,
                                        selectRef.current?.props?.value?.is_customer_or_supplier,
                                    )
                                }
                                type="button"
                                className="edit-icon focus-icon-btn position-absolute bg-transparent btn-transparent p-0"
                            >
                                <i className="fas fa-pencil text-primary"></i>
                            </button>
                        )}
                    </>
                )}
            </>
        );
    },
);

export default ReactSelect;
